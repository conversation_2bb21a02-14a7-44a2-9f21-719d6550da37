/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,ts}"
  ],
  darkMode: 'class', // Enable dark mode with class strategy
  theme: {
    extend: {
      fontFamily: {
        pacifico: ["Pacifico", "cursive"],
        gupter: ["Gupter", "serif"],
        rubik: ["Rubik", "sans-serif"],
      },
      colors: {
        // Custom theme colors
        primary: {
          light: '#8e4564',
          dark: '#6b3449',
        },
        secondary: {
          light: '#81b1d4',
          dark: '#5a7a94',
        },
        accent: {
          light: '#a06a9d',
          dark: '#7a4f77',
        },
        background: {
          light: '#ffffff',
          dark: '#1a1a1a',
        },
        surface: {
          light: '#f8fafc',
          dark: '#2d2d2d',
        },
        text: {
          primary: {
            light: '#1f2937',
            dark: '#f9fafb',
          },
          secondary: {
            light: '#6b7280',
            dark: '#d1d5db',
          },
        },
      },
    },
  },
  plugins: [],
}
