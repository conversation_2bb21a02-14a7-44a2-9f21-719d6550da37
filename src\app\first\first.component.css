

.hero-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #ffffff;
  overflow: hidden;
  background-size: cover;
}

.profile-pic {
  border-radius: 50%;
  overflow: hidden;
  width: 150px;
  height: 150px;
  margin-bottom: 20px;
}

.profile-pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-section h1 {
  font-size: 3rem;
  margin-bottom: 10px;
}

.hero-section p {
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  color: #ffffff;
  font-size: 1.5rem;
  transition: color 0.3s;
}

.social-icons a:hover {
  color: #007bff;
}

.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;

}

#bg-video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  z-index: -1;
  transform: translate(-50%, -50%);
  object-fit: cover;
}


